<script>
// Globale Variablen
let CONFIG = null;

// Lade CONFIG beim Start
window.onload = function() {
  console.log('Window onload gestartet');
  google.script.run
    .withSuccessHandler(function(config) {
      CONFIG = config;
      console.log('CONFIG geladen:', CONFIG);
      // Initialisiere die Seite erst nach dem Laden der CONFIG
      initializePage();
    })
    .withFailureHandler(function(error) {
      console.error('Fehler beim Laden der CONFIG:', error);
      showError('Fehler beim Laden der Konfiguration');
    })
    .getConfig();
};

// Initialisiere die Seite
function initializePage() {
  console.log('Initialisiere Seite');
  if (!CONFIG) {
    console.error('CONFIG noch nicht verfügbar');
    showError('Konfiguration noch nicht geladen. Bitte warten...');
    return;
  }

  // Initialisiere Materialize Komponenten
  M.AutoInit();
  
  console.log('Seite initialisiert, lade Plakate-Liste');
  // Lade die Plakate-Liste als Standard
  loadPlakate();
}

// Allgemeine Hilfsfunktionen
function showLoading() {
  const content = document.getElementById('content');
  if (!content) {
    console.error('content Element nicht gefunden');
    return;
  }
  content.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"><span class="visually-hidden">Laden...</span></div></div>';
}

function showError(message) {
  console.error('Fehler:', message);
  const content = document.getElementById('content');
  if (!content) {
    console.error('content Element nicht gefunden');
    return;
  }
  content.innerHTML = `
    <div class="alert alert-danger" role="alert">
      ${message}
    </div>
  `;
}

function showSuccess(message) {
  const content = document.getElementById('content');
  if (!content) {
    console.error('content Element nicht gefunden');
    return;
  }
  content.innerHTML = `
    <div class="alert alert-success" role="alert">
      ${message}
    </div>
  `;
}

// Konfigurationen für alle Listen-Typen
const LIST_CONFIGS = {
  // VOR MARKT - Listen mit dynamischem Hinzufügen von Einträgen
  'plakate': {
    title: 'Plakate',
    sheet: 'PLAKATE',
    type: 'vorMarkt',
    columns: [
      { editable: true, placeholder: 'Ort eingeben' },  // Ort
      { editable: true, placeholder: 'Name eingeben' },  // Person
      { editable: true, placeholder: 'Status' }  // Erledigt
    ]
  },
  'kuchen': {
    title: 'Kuchenspenden',
    sheet: 'KUCHEN',
    type: 'vorMarkt',
    columns: [
      { editable: true, placeholder: 'Name eingeben' },  // Name
      { editable: true, placeholder: 'Tag eingeben' },  // Tag
      { editable: true, placeholder: 'Info eingeben (optional)' }  // Info
    ]
  },
  'aufbau': {
    title: 'Aufbauhelfer',
    sheet: 'AUFBAU',
    type: 'vorMarkt',
    columns: [
      { editable: true, placeholder: 'Name eingeben' },  // Name
      { editable: true, placeholder: 'Montag' },  // Montag
      { editable: true, placeholder: 'Dienstag' },  // Dienstag
      { editable: true, placeholder: 'Mittwoch' },  // Mittwoch
      { editable: true, placeholder: 'Donnerstag' },  // Donnerstag
      { editable: true, placeholder: 'Freitag' }   // Freitag
    ]
  },

  // WÄHREND MARKT - Schichtlisten mit festen Zeiten
  'bar': {
    title: 'Bar-Schichten',
    sheet: 'BAR',
    type: 'waehrendMarkt',
    columns: [
      { editable: false }, // Tag
      { editable: false }, // Zeit
      { editable: true, placeholder: 'Name eingeben' }, // Person 1
      { editable: true, placeholder: 'Name eingeben' }, // Person 2
      { editable: true, placeholder: 'Name eingeben' }, // Kaffee
      { editable: true, placeholder: 'Name eingeben' }  // Bereitschaft
    ]
  },
  'pommes': {
    title: 'Pommes-Schichten',
    sheet: 'POMMES',
    type: 'waehrendMarkt',
    columns: [
      { editable: false }, // Tag
      { editable: false }, // Zeit
      { editable: true, placeholder: 'Name eingeben' }, // Person 1
      { editable: true, placeholder: 'Name eingeben' }  // Person 2
    ]
  },
  'spuel': {
    title: 'Spül-Schichten',
    sheet: 'SPUEL',
    type: 'waehrendMarkt',
    columns: [
      { editable: false }, // Tag
      { editable: false }, // Zeit
      { editable: true, placeholder: 'Name eingeben' }, // Person 1
      { editable: true, placeholder: 'Name eingeben' }  // Person 2
    ]
  },
  'kindertoepfern': {
    title: 'Kindertöpfern',
    sheet: 'KINDERTOEPFERN',
    type: 'waehrendMarkt',
    columns: [
      { editable: false }, // Tag
      { editable: false }, // Zeit
      { editable: true, placeholder: 'Name eingeben' }, // Person 1
      { editable: true, placeholder: 'Name eingeben' }  // Person 2
    ]
  },
  'lebendwerkstatt': {
    title: 'Lebendwerkstatt',
    sheet: 'LEBENDWERKSTATT',
    type: 'waehrendMarkt',
    columns: [
      { editable: false }, // Tag
      { editable: false }, // Zeit
      { editable: true, placeholder: 'Name eingeben' }, // Person 1
      { editable: true, placeholder: 'Name eingeben' }  // Person 2
    ]
  },
  'siebdruck': {
    title: 'Siebdruck',
    sheet: 'SIEBDRUCK',
    type: 'waehrendMarkt',
    columns: [
      { editable: false }, // Tag
      { editable: false }, // Zeit
      { editable: true, placeholder: 'Name eingeben' }, // Person 1
      { editable: true, placeholder: 'Name eingeben' }  // Person 2
    ]
  },
  'ausstellung': {
    title: 'Ausstellungsaufsicht',
    sheet: 'AUSSTELLUNG',
    type: 'waehrendMarkt',
    columns: [
      { editable: false }, // Tag
      { editable: false }, // Zeit
      { editable: true, placeholder: 'Name eingeben' }  // Person
    ]
  },

  // SONSTIGE INFOS - Nur Lesen
  'lineup': {
    title: 'LineUp',
    sheet: 'LINEUP',
    type: 'sonstigeInfos',
    columns: [
      { editable: false }, // Tag/ Zeit
      { editable: false }, // Künstler:innen/ Bandnamen
      { editable: false }  // Genre
    ]
  }
};

// Generische Funktion zum Laden aller Listen
function loadListeByType(type) {
  if (!CONFIG) {
    console.error('CONFIG nicht verfügbar');
    showError('Konfiguration nicht geladen. Bitte Seite neu laden.');
    return;
  }
  
  const config = LIST_CONFIGS[type];
  if (!config) {
    showError('Ungültiger Listentyp');
    return;
  }
  
  // Sheet-Namen aus CONFIG holen
  const sheetName = CONFIG.SHEETS[config.sheet];
  loadListe(config.title, sheetName, config.columns, config.type);
}

// Generische Funktion zum Laden einer Liste
function loadListe(title, sheetName, columnConfig, listType) {
  console.log('Lade Liste:', {title, sheetName, columnConfig, listType});
  
  if (!CONFIG) {
    console.error('CONFIG nicht verfügbar');
    showError('Konfiguration nicht geladen. Bitte Seite neu laden.');
    return;
  }
  
  if (!sheetName) {
    console.error('Kein Sheet-Name angegeben');
    showError('Kein Sheet-Name angegeben');
    return;
  }
  
  if (!columnConfig || !Array.isArray(columnConfig)) {
    console.error('Ungültige Spalten-Konfiguration');
    showError('Ungültige Spalten-Konfiguration');
    return;
  }
  
  showLoading();
  
  google.script.run
    .withSuccessHandler(function(data) {
      console.log('Daten erhalten:', data);
      
      if (!data) {
        console.error('Keine Daten zurückgegeben');
        showError('Keine Daten gefunden für ' + sheetName);
        return;
      }
      
      if (!data.data || !Array.isArray(data.data)) {
        console.error('Ungültige Datenstruktur:', data);
        showError('Ungültige Datenstruktur für ' + sheetName);
        return;
      }
      
      displayListe(data, title, columnConfig, listType);
    })
    .withFailureHandler(function(error) {
      console.error('Fehler beim Laden der Liste:', error);
      showError('Fehler beim Laden der Liste: ' + error);
    })
    .getListe(sheetName);
}

// Generische Funktion zum Anzeigen einer Liste
function displayListe(data, title, columnConfig, listType) {
  console.log('Display Liste:', {data, title, columnConfig, listType});
  
  const content = document.getElementById('content');
  if (!content) {
    console.error('content Element nicht gefunden');
    return;
  }

  // Für Schichtlisten (während Markt) gruppieren wir nach Tagen
  if (listType === 'waehrendMarkt') {
    // Gruppiere die Daten nach Tagen
    const groupedData = {};
    data.data.forEach(row => {
      const tag = row[0]; // Erste Spalte ist der Tag
      if (!groupedData[tag]) {
        groupedData[tag] = [];
      }
      groupedData[tag].push(row);
    });

    // Erstelle die HTML-Struktur
    content.innerHTML = `
      <div class="card">
        <div class="card-content">
          <span class="card-title">${title}</span>
          <p class="grey-text">Ansprechperson: ${data.teamleiter || ''}</p>
          <p class="grey-text">${data.unterueberschrift || ''}</p>
          
          ${Object.entries(groupedData).map(([tag, rows]) => `
            <div class="section">
              <h5 class="teal-text">${tag}</h5>
              <div class="table-container">
                <table class="striped">
                  <thead>
                    <tr>
                      ${data.headers.slice(1).map(header => `<th>${header}</th>`).join('')}
                    </tr>
                  </thead>
                  <tbody>
                    ${rows.map((row, rowIndex) => {
                      const tr = document.createElement('tr');
                      tr.innerHTML = row.slice(1).map((cell, colIndex) => {
                        // Zeit-Spalte ist nicht editierbar
                        if (colIndex === 0) {
                          return `<td>${cell}</td>`;
                        }
                        return `<td>
                          <div class="input-field">
                            <input type="text" value="${cell}" 
                                   onchange="updateListe('${data.sheetName}', ${rowIndex + data.data.indexOf(rows[0])}, ${colIndex + 1}, this.value)">
                          </div>
                        </td>`;
                      }).join('');
                      return tr.outerHTML;
                    }).join('')}
                  </tbody>
                </table>
              </div>
            </div>
          `).join('')}
        </div>
      </div>
    `;
  } else {
    // Für andere Listentypen die normale Anzeige
    content.innerHTML = `
      <div class="card">
        <div class="card-content">
          <span class="card-title">${title}</span>
          <p class="grey-text">Ansprechperson: ${data.teamleiter || ''}</p>
          <p class="grey-text">${data.unterueberschrift || ''}</p>
          
          <div class="table-container">
            <table class="striped">
              <thead>
                <tr>
                  ${data.headers.map(header => `<th>${header}</th>`).join('')}
                </tr>
              </thead>
              <tbody>
                ${data.data.map((row, rowIndex) => {
                  const tr = document.createElement('tr');
                  tr.innerHTML = row.map((cell, colIndex) => {
                    // Für sonstige Infos nur Text anzeigen
                    if (listType === 'sonstigeInfos') {
                      return `<td>${cell}</td>`;
                    }
                    
                    // Für Schichtlisten (während Markt)
                    if (listType === 'waehrendMarkt') {
                      // Erste zwei Spalten (Tag und Zeit) sind nicht editierbar
                      if (colIndex < 2) {
                        return `<td>${cell}</td>`;
                      }
                      return `<td>
                        <div class="input-field">
                          <input type="text" value="${cell}" 
                                 onchange="updateListe('${data.sheetName}', ${rowIndex}, ${colIndex}, this.value)">
                        </div>
                      </td>`;
                    }
                    
                    // Für Listen vor dem Markt
                    if (listType === 'vorMarkt') {
                      if (data.sheetName === CONFIG.SHEETS.KUCHEN && colIndex === 1) {
                        return `<td>
                          <div class="input-field">
                            <select onchange="updateListe('${data.sheetName}', ${rowIndex}, ${colIndex}, this.value)">
                              <option value="">Tag auswählen</option>
                              <option value="Freitag" ${cell === 'Freitag' ? 'selected' : ''}>Freitag</option>
                              <option value="Samstag" ${cell === 'Samstag' ? 'selected' : ''}>Samstag</option>
                              <option value="Sonntag" ${cell === 'Sonntag' ? 'selected' : ''}>Sonntag</option>
                            </select>
                          </div>
                        </td>`;
                      } else if (data.sheetName === CONFIG.SHEETS.PLAKATE && colIndex === 2) {
                        return `<td>
                          <label>
                            <input type="checkbox" class="filled-in" 
                                   ${cell === 'X' ? 'checked' : ''}
                                   onchange="updateListe('${data.sheetName}', ${rowIndex}, ${colIndex}, this.checked ? 'X' : '')">
                            <span></span>
                          </label>
                        </td>`;
                      } else if (data.sheetName === CONFIG.SHEETS.AUFBAU && colIndex > 0) {
                        return `<td>
                          <label>
                            <input type="checkbox" class="filled-in" 
                                   ${cell === 'X' ? 'checked' : ''}
                                   onchange="updateListe('${data.sheetName}', ${rowIndex}, ${colIndex}, this.checked ? 'X' : '')">
                            <span></span>
                          </label>
                        </td>`;
                      } else {
                        return `<td>
                          <div class="input-field">
                            <input type="text" value="${cell}" 
                                   onchange="updateListe('${data.sheetName}', ${rowIndex}, ${colIndex}, this.value)">
                          </div>
                        </td>`;
                      }
                    }
                  }).join('');
                  return tr.outerHTML;
                }).join('')}
                ${listType === 'vorMarkt' ? `
                  <tr class="new-entry-row">
                    ${columnConfig.map((col, colIndex) => {
                      if (data.sheetName === CONFIG.SHEETS.KUCHEN && colIndex === 1) {
                        return `<td>
                          <div class="input-field">
                            <select onchange="addNewEntry('${data.sheetName}', this, ${colIndex})">
                              <option value="">Tag auswählen</option>
                              <option value="Freitag">Freitag</option>
                              <option value="Samstag">Samstag</option>
                              <option value="Sonntag">Sonntag</option>
                            </select>
                          </div>
                        </td>`;
                      } else if (data.sheetName === CONFIG.SHEETS.PLAKATE && colIndex === 2) {
                        return `<td>
                          <label>
                            <input type="checkbox" class="filled-in" 
                                   onchange="addNewEntry('${data.sheetName}', this, ${colIndex})">
                            <span></span>
                          </label>
                        </td>`;
                      } else if (data.sheetName === CONFIG.SHEETS.AUFBAU && colIndex > 0) {
                        return `<td>
                          <label>
                            <input type="checkbox" class="filled-in" 
                                   onchange="addNewEntry('${data.sheetName}', this, ${colIndex})">
                            <span></span>
                          </label>
                        </td>`;
                      } else {
                        return `<td>
                          <div class="input-field">
                            <input type="text" 
                                   placeholder="${col.placeholder || 'Name eingeben'}"
                                   onchange="addNewEntry('${data.sheetName}', this, ${colIndex})">
                          </div>
                        </td>`;
                      }
                    }).join('')}
                  </tr>
                ` : ''}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    `;
  }

  // Initialisiere nur die Select-Elemente, um Konflikte zu vermeiden
  const selects = document.querySelectorAll('select');
  selects.forEach(select => {
    M.FormSelect.init(select);
  });
}

// Generische Funktion zum Hinzufügen neuer Einträge
function addNewEntry(sheetName, input, colIndex) {
  const row = input.closest('tr');
  const inputs = row.getElementsByTagName('input');
  const select = row.getElementsByTagName('select')[0];

  // Sammle alle Werte aus der Zeile
  const values = Array.from(inputs).map(input => {
    if (input.type === 'checkbox') {
      return input.checked ? 'X' : '';
    }
    return input.value;
  });

  // Wenn es ein Select-Element gibt, füge seinen Wert ein
  if (select) {
    values.splice(1, 0, select.value);
  }

  // Wenn mindestens ein Feld ausgefüllt wurde
  if (values.some(value => value.trim() !== '')) {
    // Speichere die Änderungen
    google.script.run
      .withSuccessHandler(function() {
        // Erfolgreich gespeichert
        M.toast({html: 'Eintrag hinzugefügt', classes: 'success'});

        // Prüfe, ob die aktuelle Zeile die letzte ist
        const tbody = row.parentNode;
        const isLastRow = row === tbody.lastElementChild;

        // Erstelle eine neue leere Zeile, wenn die aktuelle die letzte ist
        if (isLastRow) {
          createNewEntryRow(tbody, sheetName);
        }

        // Konvertiere die aktuelle Zeile zu einer normalen Datenzeile
        convertToDataRow(row, sheetName);
      })
      .withFailureHandler(function(error) {
        console.error('Fehler beim Hinzufügen:', error);
        showError('Fehler beim Hinzufügen: ' + error);
      })
      .addEintrag(sheetName, values);
  }
}

// Hilfsfunktion zum Erstellen einer neuen Eingabezeile
function createNewEntryRow(tbody, sheetName) {
  const newRow = document.createElement('tr');
  newRow.className = 'new-entry-row';

  // Bestimme die Spaltenanzahl basierend auf dem Sheet-Typ
  const config = Object.values(LIST_CONFIGS).find(c => CONFIG.SHEETS[c.sheet] === sheetName);
  if (!config) return;

  const columns = config.columns;

  newRow.innerHTML = columns.map((col, colIndex) => {
    if (sheetName === CONFIG.SHEETS.KUCHEN && colIndex === 1) {
      return `<td>
        <div class="input-field">
          <select onchange="addNewEntry('${sheetName}', this, ${colIndex})">
            <option value="">Tag auswählen</option>
            <option value="Freitag">Freitag</option>
            <option value="Samstag">Samstag</option>
            <option value="Sonntag">Sonntag</option>
          </select>
        </div>
      </td>`;
    } else if (sheetName === CONFIG.SHEETS.PLAKATE && colIndex === 2) {
      return `<td>
        <label>
          <input type="checkbox" class="filled-in"
                 onchange="addNewEntry('${sheetName}', this, ${colIndex})">
          <span></span>
        </label>
      </td>`;
    } else if (sheetName === CONFIG.SHEETS.AUFBAU && colIndex > 0) {
      return `<td>
        <label>
          <input type="checkbox" class="filled-in"
                 onchange="addNewEntry('${sheetName}', this, ${colIndex})">
          <span></span>
        </label>
      </td>`;
    } else {
      return `<td>
        <div class="input-field">
          <input type="text"
                 placeholder="${col.placeholder || 'Name eingeben'}"
                 onchange="addNewEntry('${sheetName}', this, ${colIndex})">
        </div>
      </td>`;
    }
  }).join('');

  tbody.appendChild(newRow);

  // Initialisiere nur die neuen Select-Elemente
  const selects = newRow.querySelectorAll('select');
  selects.forEach(select => {
    M.FormSelect.init(select);
  });
}

// Hilfsfunktion zum Konvertieren einer Eingabezeile zu einer Datenzeile
function convertToDataRow(row, sheetName) {
  row.className = '';

  // Hole die aktuellen Werte
  const inputs = row.getElementsByTagName('input');
  const select = row.getElementsByTagName('select')[0];

  const values = Array.from(inputs).map(input => {
    if (input.type === 'checkbox') {
      return input.checked ? 'X' : '';
    }
    return input.value;
  });

  if (select) {
    values.splice(1, 0, select.value);
  }

  // Bestimme den Row-Index für Update-Funktionen
  const tbody = row.parentNode;
  const rowIndex = Array.from(tbody.children).indexOf(row);

  // Ersetze den Inhalt mit editierbaren Feldern
  row.innerHTML = values.map((value, colIndex) => {
    if (sheetName === CONFIG.SHEETS.KUCHEN && colIndex === 1) {
      return `<td>
        <div class="input-field">
          <select onchange="updateListe('${sheetName}', ${rowIndex}, ${colIndex}, this.value)">
            <option value="">Tag auswählen</option>
            <option value="Freitag" ${value === 'Freitag' ? 'selected' : ''}>Freitag</option>
            <option value="Samstag" ${value === 'Samstag' ? 'selected' : ''}>Samstag</option>
            <option value="Sonntag" ${value === 'Sonntag' ? 'selected' : ''}>Sonntag</option>
          </select>
        </div>
      </td>`;
    } else if (sheetName === CONFIG.SHEETS.PLAKATE && colIndex === 2) {
      return `<td>
        <label>
          <input type="checkbox" class="filled-in"
                 ${value === 'X' ? 'checked' : ''}
                 onchange="updateListe('${sheetName}', ${rowIndex}, ${colIndex}, this.checked ? 'X' : '')">
          <span></span>
        </label>
      </td>`;
    } else if (sheetName === CONFIG.SHEETS.AUFBAU && colIndex > 0) {
      return `<td>
        <label>
          <input type="checkbox" class="filled-in"
                 ${value === 'X' ? 'checked' : ''}
                 onchange="updateListe('${sheetName}', ${rowIndex}, ${colIndex}, this.checked ? 'X' : '')">
          <span></span>
        </label>
      </td>`;
    } else {
      return `<td>
        <div class="input-field">
          <input type="text" value="${value}"
                 onchange="updateListe('${sheetName}', ${rowIndex}, ${colIndex}, this.value)">
        </div>
      </td>`;
    }
  }).join('');

  // Initialisiere nur die neuen Select-Elemente in dieser Zeile
  const selects = row.querySelectorAll('select');
  selects.forEach(select => {
    M.FormSelect.init(select);
  });
}

// Ersetze die alten Load-Funktionen durch Aufrufe der neuen Funktion
function loadPlakate() {
  loadListeByType('plakate');
}

function addPlakateEintrag() {
  const ort = document.getElementById('newOrt').value;
  const person = document.getElementById('newPerson').value;
  
  if (!ort) {
    showError('Bitte mindestens einen Ort eingeben');
    return;
  }
  
  google.script.run
    .withSuccessHandler(function() {
      // Nach erfolgreichem Hinzufügen die Liste neu laden
      loadPlakate();
      // Felder leeren
      document.getElementById('newOrt').value = '';
      document.getElementById('newPerson').value = '';
    })
    .withFailureHandler(function(error) {
      console.error('Fehler beim Hinzufügen:', error);
      showError('Fehler beim Hinzufügen: ' + error);
    })
    .addEintrag(CONFIG.SHEETS.PLAKATE, [ort, person]);
}

function addPersonToExistingEntry(rowIndex) {
  const person = document.getElementById(`person_${rowIndex}`).value;
  
  if (!person) {
    showError('Bitte einen Namen eingeben');
    return;
  }
  
  google.script.run
    .withSuccessHandler(function() {
      // Nach erfolgreichem Hinzufügen die Liste neu laden
      loadPlakate();
    })
    .withFailureHandler(function(error) {
      console.error('Fehler beim Hinzufügen:', error);
      showError('Fehler beim Hinzufügen: ' + error);
    })
    .updatePlakateEintrag(CONFIG.SHEETS.PLAKATE, rowIndex, person);
}

// Update-Funktion
function updateListe(sheetName, rowIndex, columnIndex, value) {
  console.log('Update Liste:', {sheetName, rowIndex, columnIndex, value});
  google.script.run
    .withSuccessHandler(function(success) {
      if (!success) {
        M.toast({html: 'Fehler beim Speichern der Änderung', classes: 'error'});
      } else {
        M.toast({html: 'Änderung gespeichert', classes: 'success'});
      }
    })
    .withFailureHandler(function(error) {
      M.toast({html: 'Fehler beim Speichern der Änderung: ' + error, classes: 'error'});
    })
    .updateListe(sheetName, rowIndex, columnIndex, value);
}
</script> 
