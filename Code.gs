// Sheet-Namen als Konstanten
const SHEET_BAR = 'BarSchichten';
const SHEET_POMMES = 'PommesSchichten';
const SHEET_SPUEL = 'SpuelSchichten';
const SHEET_KINDERTOEPFERN = 'Kindertoepfern';
const SHEET_LEBENDWERKSTATT = 'Lebendwerkstatt';
const SHEET_SIEBDRUCK = 'Siebdruck';
const SHEET_AUSSTELLUNG = 'Ausstellungsaufsicht';
const SHEET_KUCHEN = 'Kuchenspenden';
const SHEET_PLAKATE = 'Plakate';
const SHEET_AUFBAU = 'Aufbauhelfer';
const SHEET_LINEUP = 'LineUp';

const CONFIG = {
  SPREADSHEET_ID: '1qsGF8eQY8WuV-HTETep78VUsylGgox2jdvdtFqZhjiw',
  SHEETS: {
    BAR: SHEET_BAR,
    POMMES: SHEET_POMMES,
    SPUEL: SHEET_SPUEL,
    KINDERTOEPFERN: SHEET_KINDERTOEPFERN,
    LEBENDWERKSTATT: SHEET_LEBENDWERKSTATT,
    SIEBDRUCK: SHEET_SIEBDRUCK,
    AUSSTELLUNG: SHEET_AUSSTELLUNG,
    KUCHEN: SHEET_KUCHEN,
    PLAKATE: SHEET_PLAKATE,
    AUFBAU: SHEET_AUFBAU,
    LINEUP: SHEET_LINEUP
  }
};

function doGet() {
  return HtmlService.createTemplateFromFile('index')
    .evaluate()
    .setTitle('Markt_Listen')
    .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL)
    .addMetaTag('viewport', 'width=device-width, initial-scale=1');
}

function include(filename) {
  if (!filename) {
    console.error('Kein Dateiname angegeben');
    return '';
  }
  
  try {
    return HtmlService.createHtmlOutputFromFile(filename).getContent();
  } catch (error) {
    console.error('Fehler beim Laden des Templates:', filename, error);
    return '';
  }
}

// Hilfsfunktion zum Abrufen eines Sheets
function getSheetByName(sheetName) {
  try {
    const ss = SpreadsheetApp.openById(CONFIG.SPREADSHEET_ID);
    return ss.getSheetByName(sheetName);
  } catch (error) {
    console.error('Fehler beim Abrufen des Sheets:', error);
    return null;
  }
}

// Konfiguration für Client bereitstellen
function getConfig() {
  return {
    SHEETS: CONFIG.SHEETS
  };
}

function getListe(sheetName) {
  const sheet = getSheetByName(sheetName);
  if (!sheet) return null;
  
  try {
    const dataRange = sheet.getDataRange();
    const values = dataRange.getValues();
    
    // Schnellerer Zugriff auf Metadaten
    const [headers, ...data] = values.slice(3);
    
    return {
      headers,
      data,
      sheetName,
      teamleiter: values[0][1],
      unterueberschrift: values[1][1]
    };
  } catch (error) {
    console.error('Fehler in getListe:', error);
    return null;
  }
}

function updateListe(sheetName, rowIndex, columnIndex, value) {
  console.log('Starte updateListe für:', {sheetName, rowIndex, columnIndex, value});
  const sheet = getSheetByName(sheetName);
  if (!sheet) {
    console.error('Sheet nicht gefunden:', sheetName);
    return false;
  }
  
  try {
    // +5 weil: +1 für 0-basierter Index, +4 für Header-Zeile und Metadaten
    sheet.getRange(rowIndex + 5, columnIndex + 1).setValue(value);
    console.log('Schicht erfolgreich aktualisiert');
    return true;
  } catch (error) {
    console.error('Fehler beim Aktualisieren der Schicht:', error);
    return false;
  }
}

function addEintrag(sheetName, values) {
  const sheet = getSheetByName(sheetName);
  if (!sheet) {
    throw new Error('Sheet nicht gefunden: ' + sheetName);
  }
  
  // Füge neue Zeile am Ende hinzu
  sheet.appendRow(values);
}

function updatePlakateEintrag(sheetName, rowIndex, person) {
  const sheet = getSheetByName(sheetName);
  if (!sheet) {
    throw new Error('Sheet nicht gefunden: ' + sheetName);
  }
  
  // Die erste Zeile ist der Header (Zeile 4), daher +4 zum Index
  const rowToUpdate = rowIndex + 5;
  sheet.getRange(rowToUpdate, 2).setValue(person);
}
