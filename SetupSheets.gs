// Hilfsfunktion zum Setzen der Metadaten für ein Sheet
function setSheetMetadata(sheet, teamleiter, unterueberschrift) {
  // Lösche alte Metadaten falls vorhanden
  const oldMetadata = sheet.getRange('A1:B2');
  if (oldMetadata) {
    oldMetadata.clearContent();
  }
  
  // Setze neue Metadaten
  sheet.getRange('A1:B2').setValues([
    ['Ansprechperson', teamleiter],
    ['Unterüberschrift', unterueberschrift]
  ]);
  
  // Formatiere Metadaten
  sheet.getRange('A1:B2')
    .setBackground('#f8f9fa')
    .setFontWeight('bold')
    .setHorizontalAlignment('left')
    .setVerticalAlignment('middle')
    .setWrap(true);
}

function setupSheets() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  
  // Hilfsfunktion zum Erstellen oder Aktualisieren eines Sheets
  function getOrCreateSheet(sheetName) {
    let sheet = ss.getSheetByName(sheetName);
    if (!sheet) {
      sheet = ss.insertSheet(sheetName);
    }
    return sheet;
  }
  
  // VOR MARKT
  // Plakate
  let sheet = getOrCreateSheet('Plakate');
  setSheetMetadata(sheet, 'Dennis', 'Übersicht der verteilten Plakate und Flyer');
  sheet.getRange('A4:C4').setValues([['Ort', 'Person', 'Erledigt']]);
  sheet.setFrozenRows(4);
  
  // Kuchenspenden
  sheet = getOrCreateSheet('Kuchenspenden');
  setSheetMetadata(sheet, 'Toni', 'Wer bringt wann einen Kuchen vorbei? =)');
  sheet.getRange('A4:C4').setValues([['Name', 'Tag', 'Info (optional)']]);
  sheet.setFrozenRows(4);
  
  // Aufbauhelfer
  sheet = getOrCreateSheet('Aufbauhelfer');
  setSheetMetadata(sheet, '', 'WICHTIG: Treffen für Alle Helfer und Helferinnen: FR, 27.06. um 18 Uhr in Pfettrach!');
  sheet.getRange('A4:F4').setValues([['Name', 'Montag', 'Dienstag', 'Mittwoch', 'Donnerstag', 'Freitag']]);
  sheet.setFrozenRows(4);
  
  // WÄHREND MARKT
  // Bar Schichten
  sheet = getOrCreateSheet('BarSchichten');
  setSheetMetadata(sheet, 'Andrea', 'Je 3 Personen pro Schicht - eine Person für Bedienung der Kaffeemaschine!\n1 Person im Bereitschaftsdienst');
  sheet.getRange('A4:F4').setValues([['Tag', 'Zeit', 'Person 1', 'Person 2', 'Kaffee', 'Bereitschaft']]);
  sheet.getRange('A5:A8').setValues([['Samstag'], ['Samstag'], ['Samstag'], ['Samstag']]);
  sheet.getRange('A9:A12').setValues([['Sonntag'], ['Sonntag'], ['Sonntag'], ['Sonntag']]);
  sheet.getRange('B5:B8').setValues([['10-12 Uhr'], ['12-14 Uhr'], ['14-16 Uhr'], ['16-18 Uhr']]);
  sheet.getRange('B9:B12').setValues([['10-12 Uhr'], ['12-14 Uhr'], ['14-16 Uhr'], ['16-18 Uhr']]);
  sheet.setFrozenRows(4);
  
  // Pommes Schichten
  sheet = getOrCreateSheet('PommesSchichten');
  setSheetMetadata(sheet, 'Toni', 'Pommesparty mit Toni - 2 Personen je 2h leckker Pommes Frittes kochen!!');
  sheet.getRange('A4:D4').setValues([['Tag', 'Zeit', 'Person 1', 'Person 2']]);
  sheet.getRange('A5:A8').setValues([['Samstag'], ['Samstag'], ['Samstag'], ['Samstag']]);
  sheet.getRange('A9:A12').setValues([['Sonntag'], ['Sonntag'], ['Sonntag'], ['Sonntag']]);
  sheet.getRange('B5:B8').setValues([['10-12 Uhr'], ['12-14 Uhr'], ['14-16 Uhr'], ['16-18 Uhr']]);
  sheet.getRange('B9:B12').setValues([['10-12 Uhr'], ['12-14 Uhr'], ['14-16 Uhr'], ['16-18 Uhr']]);
  sheet.setFrozenRows(4);
  
  // Spül Schichten
  sheet = getOrCreateSheet('SpuelSchichten');
  setSheetMetadata(sheet, 'Paul', 'Paul organisiert Schichten mit Spülboy-Gruppe selbstständig.\nSchichten: (je 2 Personen pro Schicht)');
  sheet.getRange('A4:D4').setValues([['Tag', 'Zeit', 'Person 1', 'Person 2']]);
  sheet.getRange('A5:A10').setValues([
    ['Freitag'],
    ['Freitag'],
    ['Samstag'],
    ['Samstag'],
    ['Sonntag'],
    ['Sonntag']
  ]);
  sheet.getRange('B5:B10').setValues([
    ['11:30-14:00'],
    ['16:00-20:00'],
    ['09:30-13:30'],
    ['13:30-17:30'],
    ['09:30-13:30'],
    ['13:30-17:30']
  ]);
  sheet.setFrozenRows(4);
  
  // Kindertöpfern
  sheet = getOrCreateSheet('Kindertoepfern');
  setSheetMetadata(sheet, 'KSL', 'für die SchülerInnen der KSL - herzlichen Dank fürs Helfen!\nSchichten: (je 2 Personen pro Schicht)');
  sheet.getRange('A4:D4').setValues([['Tag', 'Zeit', 'Person 1', 'Person 2']]);
  sheet.getRange('A5:A10').setValues([
    ['Samstag'],
    ['Samstag'],
    ['Samstag'],
    ['Sonntag'],
    ['Sonntag'],
    ['Sonntag']
  ]);
  sheet.getRange('B5:B10').setValues([
    ['09:30-12:30'],
    ['12:30-15:30'],
    ['15:30-18:30'],
    ['09:30-12:30'],
    ['12:30-15:30'],
    ['15:30-18:30']
  ]);
  sheet.setFrozenRows(4);
  
  // Lebendwerkstatt
  sheet = getOrCreateSheet('Lebendwerkstatt');
  setSheetMetadata(sheet, 'KSL', 'für die SchülerInnen der KSL - herzlichen Dank fürs Helfen!\nSchichten: (je 2 Personen pro Schicht)');
  sheet.getRange('A4:D4').setValues([['Tag', 'Zeit', 'Person 1', 'Person 2']]);
  sheet.getRange('A5:A10').setValues([
    ['Samstag'],
    ['Samstag'],
    ['Samstag'],
    ['Sonntag'],
    ['Sonntag'],
    ['Sonntag']
  ]);
  sheet.getRange('B5:B10').setValues([
    ['09:30-12:30'],
    ['12:30-15:30'],
    ['15:30-18:30'],
    ['09:30-12:30'],
    ['12:30-15:30'],
    ['15:30-18:30']
  ]);
  sheet.setFrozenRows(4);
  
  // Siebdruck
  sheet = getOrCreateSheet('Siebdruck');
  setSheetMetadata(sheet, 'Pauline oder Luzie', 'für die SchülerInnen der KSL - herzlichen Dank fürs Helfen!\nSchichten: (je 2 Personen pro Schicht)');
  sheet.getRange('A4:D4').setValues([['Tag', 'Zeit', 'Person 1', 'Person 2']]);
  sheet.getRange('A5:A10').setValues([
    ['Samstag'],
    ['Samstag'],
    ['Samstag'],
    ['Sonntag'],
    ['Sonntag'],
    ['Sonntag']
  ]);
  sheet.getRange('B5:B10').setValues([
    ['09:30-12:30'],
    ['12:30-15:30'],
    ['15:30-18:30'],
    ['09:30-12:30'],
    ['12:30-15:30'],
    ['15:30-18:30']
  ]);
  sheet.setFrozenRows(4);
  
  // Ausstellungsaufsicht
  sheet = getOrCreateSheet('Ausstellungsaufsicht');
  setSheetMetadata(sheet, '', 'Merchstand und/oder Ausstellungsraumaufsicht - wird demnächst noch geklärt.\nSchichten: (je 1 Person pro Schicht)');
  sheet.getRange('A4:C4').setValues([['Tag', 'Zeit', 'Person']]);
  sheet.getRange('A5:A8').setValues([['Samstag'], ['Samstag'], ['Samstag'], ['Samstag']]);
  sheet.getRange('A9:A12').setValues([['Sonntag'], ['Sonntag'], ['Sonntag'], ['Sonntag']]);
  sheet.getRange('B5:B8').setValues([['10-12 Uhr'], ['12-14 Uhr'], ['14-16 Uhr'], ['16-18 Uhr']]);
  sheet.getRange('B9:B12').setValues([['10-12 Uhr'], ['12-14 Uhr'], ['14-16 Uhr'], ['16-18 Uhr']]);
  sheet.setFrozenRows(4);
  
  // SONSTIGE INFOS
  // LineUp
  sheet = getOrCreateSheet('LineUp');
  setSheetMetadata(sheet, 'Jakob oder Flo?', 'Bühnenprogramm');
  sheet.getRange('A4:C4').setValues([['Tag/ Zeit', 'Künstler:innen/ Bandnamen', 'Genre']]);
  sheet.getRange('A5:A12').setValues([
    ['Sa, 12:00 - 13:00 Uhr'],
    ['Sa, 13:30 - 14:30 Uhr'],
    ['Sa, 15:00 - 16:00 Uhr'],
    ['Sa, 16:30 - 17:30 Uhr'],
    ['So, 12:00 - 13:00 Uhr'],
    ['So, 13:30 - 14:30 Uhr'],
    ['So, 15:00 - 16:00 Uhr'],
    ['So, 16:30 - 17:30 Uhr']
  ]);
  sheet.getRange('B5:B12').setValues([
    ['Zigori'],
    ['Hanging Lamp Trio'],
    ['Trattoria Tristezza'],
    ['ROMEO HALL UND STEFAN KIRNER'],
    ['KASEKO Projekt'],
    ['Wollstiefel'],
    ['Walther, angenehm'],
    ['Fraenko']
  ]);
  sheet.getRange('C5:C12').setValues([
    ['Outlaw guitar songs (Singer/Songwriter)'],
    ['Jam Band'],
    ['Chanson-Punk'],
    ['Singer Songwriter aus Gerzen DARK COUNTRY'],
    ['Jazz'],
    ['Akustikpunk mit Gitarre und Cajon'],
    ['Psychedelic Rock / Hip Hop / Jazz'],
    ['R&B, Hip Hop und Vibes']
  ]);
  sheet.setFrozenRows(4);
  
  // Formatiere alle Sheets
  const sheets = ss.getSheets();
  sheets.forEach(sheet => {
    const lastColumn = sheet.getLastColumn();
    if (lastColumn > 0) {
      // Setze Spaltenbreiten
      sheet.setColumnWidths(1, lastColumn, 150);
      
      // Formatiere Header
      const headerRange = sheet.getRange(4, 1, 1, lastColumn);
      headerRange.setBackground('#2c3e50')
                 .setFontColor('white')
                 .setFontWeight('bold');
      
      // Formatiere Zellen
      const lastRow = sheet.getLastRow();
      if (lastRow > 4) {
        const dataRange = sheet.getRange(5, 1, lastRow - 4, lastColumn);
        dataRange.setHorizontalAlignment('left')
                 .setVerticalAlignment('middle')
                 .setWrap(true);
      }
    }
  });
  
  Logger.log('Sheets erfolgreich eingerichtet');
} 