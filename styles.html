<style>
  /* Grundlegende Styles */
  body {
    display: flex;
    min-height: 100vh;
    flex-direction: column;
    background-color: #f5f5f5;
  }

  main {
    flex: 1 0 auto;
  }

  /* Navigation */
  .brand-logo {
    font-size: 1.5rem !important;
  }

  .sidenav {
    width: 250px;
  }

  .sidenav h5 {
    margin: 0;
    padding: 16px;
    color: #26a69a;
  }

  /* Content Styles */
  .card {
    margin: 0.5rem 0 1rem 0;
  }

  .card .card-content {
    padding: 1rem;
  }

  /* Table Styles */
  .table-container {
    overflow-x: auto;
    margin: 0.5rem 0;
  }

  table {
    width: 100%;
    border-collapse: collapse;
  }

  th {
    background-color: #26a69a;
    color: white;
    font-weight: 500;
    padding: 8px 12px;
    font-size: 0.9rem;
  }

  td, th {
    padding: 6px 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
    font-size: 0.9rem;
    line-height: 1.2;
  }

  /* Form Styles */
  .input-field {
    margin: 0;
  }

  .input-field input[type=text],
  .input-field input[type=email],
  .input-field input[type=password] {
    margin: 0;
    height: 2rem !important;
    font-size: 0.9rem !important;
  }

  .input-field select {
    margin: 0;
    height: 2rem !important;
    font-size: 0.9rem !important;
    display: block;
  }

  .input-field label {
    font-size: 0.9rem;
  }

  /* Select Dropdown Styles in Tables */
  td .input-field .select-wrapper {
    margin: 0;
  }

  td .input-field .select-wrapper input.select-dropdown {
    height: 2rem !important;
    line-height: 2rem !important;
    margin: 0;
    border: none;
    border-bottom: 1px solid #9e9e9e;
  }

  td .input-field .select-wrapper .caret {
    right: 0;
    top: 0.5rem;
  }

  /* Prevent dropdown from breaking table layout */
  td .input-field .select-wrapper ul.dropdown-content {
    position: absolute !important;
    z-index: 1000;
    min-width: 100px;
  }

  /* Mobile Optimizations */
  @media only screen and (max-width: 600px) {
    .container {
      width: 100%;
      padding: 0 10px;
    }

    .card {
      margin: 0.5rem 0;
    }

    .card .card-content {
      padding: 0.75rem;
    }

    td, th {
      padding: 4px 8px;
    }

    /* Make inputs more touch-friendly on mobile */
    input[type="text"],
    input[type="email"],
    input[type="password"],
    select {
      font-size: 16px !important; /* Prevents zoom on iOS */
      height: 2.5rem !important;
    }

    /* Mobile select dropdown adjustments */
    td .input-field .select-wrapper input.select-dropdown {
      height: 2.5rem !important;
      line-height: 2.5rem !important;
      font-size: 16px !important;
    }

    /* Larger touch targets for checkboxes */
    [type="checkbox"]+span:not(.lever) {
      padding-left: 35px;
    }

    [type="checkbox"]+span:not(.lever):before,
    [type="checkbox"]:not(.filled-in)+span:not(.lever):after {
      width: 20px;
      height: 20px;
    }
  }

  /* New Entry Row Styles */
  .new-entry-row {
    background-color: #f5f5f5;
  }

  .new-entry-row td {
    padding: 4px 8px;
  }

  .new-entry-row input,
  .new-entry-row select {
    margin: 0;
    height: 2rem !important;
  }

  /* Success/Error Messages */
  .toast {
    border-radius: 4px;
  }

  .toast.success {
    background-color: #4caf50;
  }

  .toast.error {
    background-color: #f44336;
  }
</style> 
